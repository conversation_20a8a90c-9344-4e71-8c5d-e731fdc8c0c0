import { useState } from 'react'
import { BarChart3, Eye, Users, TrendingUp, Calendar, Download, Filter } from 'lucide-react'

const AnalyticsDashboard = () => {
  const [timeRange, setTimeRange] = useState('7d')
  
  // Mock analytics data
  const analyticsData = {
    overview: {
      totalViews: 1247,
      uniqueVisitors: 892,
      profileClicks: 156,
      contactRequests: 23
    },
    viewsOverTime: [
      { date: '2024-01-01', views: 45 },
      { date: '2024-01-02', views: 52 },
      { date: '2024-01-03', views: 38 },
      { date: '2024-01-04', views: 67 },
      { date: '2024-01-05', views: 71 },
      { date: '2024-01-06', views: 58 },
      { date: '2024-01-07', views: 63 }
    ],
    topSections: [
      { section: 'Portfolio Projects', views: 423, percentage: 34 },
      { section: 'Skills & Experience', views: 312, percentage: 25 },
      { section: 'About Me', views: 287, percentage: 23 },
      { section: 'Contact Information', views: 225, percentage: 18 }
    ],
    visitorSources: [
      { source: 'Direct', visitors: 345, percentage: 39 },
      { source: 'LinkedIn', visitors: 267, percentage: 30 },
      { source: 'Google Search', visitors: 178, percentage: 20 },
      { source: 'Twitter', visitors: 67, percentage: 7 },
      { source: 'Other', visitors: 35, percentage: 4 }
    ],
    demographics: {
      countries: [
        { country: 'United States', visitors: 456, percentage: 51 },
        { country: 'United Kingdom', visitors: 123, percentage: 14 },
        { country: 'Canada', visitors: 89, percentage: 10 },
        { country: 'Germany', visitors: 67, percentage: 8 },
        { country: 'Australia', visitors: 45, percentage: 5 },
        { country: 'Others', visitors: 112, percentage: 12 }
      ]
    }
  }

  const StatCard = ({ title, value, change, icon: Icon, color = 'primary' }) => (
    <div className="bg-white rounded-lg border border-gray-200 p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-3xl font-bold text-gray-900 mt-2">{value.toLocaleString()}</p>
          {change && (
            <p className={`text-sm mt-2 flex items-center ${
              change > 0 ? 'text-green-600' : 'text-red-600'
            }`}>
              <TrendingUp className="w-4 h-4 mr-1" />
              {change > 0 ? '+' : ''}{change}% from last period
            </p>
          )}
        </div>
        <div className={`w-12 h-12 bg-${color}-100 rounded-lg flex items-center justify-center`}>
          <Icon className={`w-6 h-6 text-${color}-600`} />
        </div>
      </div>
    </div>
  )

  return (
    <div className="max-w-7xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Analytics Dashboard</h1>
          <p className="text-gray-600 mt-2">
            Track your portfolio performance and visitor engagement
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
          >
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
            <option value="1y">Last year</option>
          </select>
          <button className="btn btn-outline">
            <Filter className="w-4 h-4" />
            Filters
          </button>
          <button className="btn btn-primary">
            <Download className="w-4 h-4" />
            Export Report
          </button>
        </div>
      </div>

      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Total Profile Views"
          value={analyticsData.overview.totalViews}
          change={12.5}
          icon={Eye}
          color="primary"
        />
        <StatCard
          title="Unique Visitors"
          value={analyticsData.overview.uniqueVisitors}
          change={8.2}
          icon={Users}
          color="primary"
        />
        <StatCard
          title="Profile Clicks"
          value={analyticsData.overview.profileClicks}
          change={-2.1}
          icon={BarChart3}
          color="primary"
        />
        <StatCard
          title="Contact Requests"
          value={analyticsData.overview.contactRequests}
          change={15.7}
          icon={Calendar}
          color="primary"
        />
      </div>

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Views Over Time */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Views Over Time</h3>
          <div className="h-64 flex items-end justify-between space-x-2">
            {analyticsData.viewsOverTime.map((day, index) => (
              <div key={index} className="flex flex-col items-center flex-1">
                <div
                  className="bg-primary-500 rounded-t w-full"
                  style={{ height: `${(day.views / 80) * 100}%` }}
                ></div>
                <span className="text-xs text-gray-500 mt-2">
                  {new Date(day.date).toLocaleDateString('en-US', { weekday: 'short' })}
                </span>
                <span className="text-xs font-medium text-gray-900">{day.views}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Top Sections */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Most Viewed Sections</h3>
          <div className="space-y-4">
            {analyticsData.topSections.map((section, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-sm font-medium text-gray-900">{section.section}</span>
                    <span className="text-sm text-gray-600">{section.views} views</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-primary-500 h-2 rounded-full"
                      style={{ width: `${section.percentage}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Visitor Sources and Demographics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Visitor Sources */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Visitor Sources</h3>
          <div className="space-y-3">
            {analyticsData.visitorSources.map((source, index) => (
              <div key={index} className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-900">{source.source}</span>
                <div className="flex items-center space-x-3">
                  <div className="w-24 bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-primary-500 h-2 rounded-full"
                      style={{ width: `${source.percentage}%` }}
                    ></div>
                  </div>
                  <span className="text-sm text-gray-600 w-12 text-right">
                    {source.percentage}%
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Geographic Distribution */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Geographic Distribution</h3>
          <div className="space-y-3">
            {analyticsData.demographics.countries.map((country, index) => (
              <div key={index} className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-900">{country.country}</span>
                <div className="flex items-center space-x-3">
                  <span className="text-sm text-gray-600">{country.visitors} visitors</span>
                  <span className="text-sm text-gray-500 w-8 text-right">
                    {country.percentage}%
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
        <div className="space-y-3">
          <div className="flex items-center justify-between py-2 border-b border-gray-100">
            <div className="flex items-center space-x-3">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span className="text-sm text-gray-900">New profile view from LinkedIn</span>
            </div>
            <span className="text-xs text-gray-500">2 minutes ago</span>
          </div>
          <div className="flex items-center justify-between py-2 border-b border-gray-100">
            <div className="flex items-center space-x-3">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <span className="text-sm text-gray-900">Contact form submitted</span>
            </div>
            <span className="text-xs text-gray-500">15 minutes ago</span>
          </div>
          <div className="flex items-center justify-between py-2 border-b border-gray-100">
            <div className="flex items-center space-x-3">
              <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
              <span className="text-sm text-gray-900">Portfolio project viewed</span>
            </div>
            <span className="text-xs text-gray-500">1 hour ago</span>
          </div>
        </div>
      </div>
    </div>
  )
}

export default AnalyticsDashboard
