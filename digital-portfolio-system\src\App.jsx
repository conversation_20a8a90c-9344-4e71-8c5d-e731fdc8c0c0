import { useState } from 'react'
import { User, Briefcase, Search, Star, MessageCircle, Settings } from 'lucide-react'
import Header from './components/Header'
import Hero from './components/Hero'
import FreelancerProfiles from './components/FreelancerProfiles'
import SearchFilters from './components/SearchFilters'
import Footer from './components/Footer'

function App() {
  const [currentView, setCurrentView] = useState('home')
  const [searchFilters, setSearchFilters] = useState({
    skills: '',
    experience: '',
    location: '',
    priceRange: ''
  })

  const handleViewChange = (view) => {
    setCurrentView(view)
  }

  const handleFilterChange = (filters) => {
    setSearchFilters(filters)
  }

  return (
    <div className="min-h-screen bg-gray-50" style={{ minHeight: '100vh', backgroundColor: '#f9fafb' }}>
      <Header currentView={currentView} onViewChange={handleViewChange} />

      <main>
        {currentView === 'home' && (
          <>
            {/* Debug test */}
            <div style={{ padding: '20px', backgroundColor: 'red', color: 'white', textAlign: 'center' }}>
              CSS Test - If you see this red box, React is working but CSS classes might not be loading
            </div>
            <Hero onViewChange={handleViewChange} />
            <section className="py-16">
              <div className="container">
                <div className="text-center mb-12">
                  <h2 className="text-3xl font-bold text-gray-900 mb-4">
                    Why Choose Our Platform?
                  </h2>
                  <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                    Experience the next generation of freelancer-client connections with enhanced customization and powerful features.
                  </p>
                </div>

                <div className="grid md:grid-cols-3 gap-8">
                  <div className="card text-center">
                    <div className="card-body">
                      <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <User className="w-6 h-6 text-primary-600" />
                      </div>
                      <h3 className="text-xl font-semibold mb-3">Enhanced Customization</h3>
                      <p className="text-gray-600">
                        Create fully personalized profiles that reflect your unique brand and skills, standing out from the competition.
                      </p>
                    </div>
                  </div>

                  <div className="card text-center">
                    <div className="card-body">
                      <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <Search className="w-6 h-6 text-primary-600" />
                      </div>
                      <h3 className="text-xl font-semibold mb-3">Advanced Filtering</h3>
                      <p className="text-gray-600">
                        Powerful search and filtering tools help clients find the perfect freelancer based on detailed criteria.
                      </p>
                    </div>
                  </div>

                  <div className="card text-center">
                    <div className="card-body">
                      <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <Star className="w-6 h-6 text-primary-600" />
                      </div>
                      <h3 className="text-xl font-semibold mb-3">Analytics & Insights</h3>
                      <p className="text-gray-600">
                        Track profile views, engagement metrics, and optimize your presence with detailed analytics.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </section>
          </>
        )}

        {currentView === 'browse' && (
          <section className="py-8">
            <div className="container">
              <div className="flex flex-col lg:flex-row gap-8">
                <div className="lg:w-1/4">
                  <SearchFilters onFilterChange={handleFilterChange} />
                </div>
                <div className="lg:w-3/4">
                  <FreelancerProfiles filters={searchFilters} />
                </div>
              </div>
            </div>
          </section>
        )}

        {currentView === 'profile' && (
          <section className="py-8">
            <div className="container">
              <div className="card">
                <div className="card-header">
                  <h2 className="text-2xl font-bold">Create Your Profile</h2>
                  <p className="text-gray-600 mt-2">Build your professional digital portfolio</p>
                </div>
                <div className="card-body">
                  <div className="text-center py-12">
                    <Briefcase className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-xl font-semibold mb-2">Profile Creation Coming Soon</h3>
                    <p className="text-gray-600">
                      Advanced profile customization tools are being developed to give you complete control over your digital presence.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </section>
        )}
      </main>

      <Footer />
    </div>
  )
}

export default App
