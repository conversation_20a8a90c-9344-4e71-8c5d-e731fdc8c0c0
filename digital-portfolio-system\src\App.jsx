import { useState } from 'react'
import { User, Briefcase, Search, Star, MessageCircle, Settings } from 'lucide-react'
import Header from './components/Header'
import Hero from './components/Hero'
import FreelancerProfiles from './components/FreelancerProfiles'
import SearchFilters from './components/SearchFilters'
import Footer from './components/Footer'
import AuthModal from './components/AuthModal'
import ProfileBuilder from './components/ProfileBuilder'
import AnalyticsDashboard from './components/AnalyticsDashboard'
import Calendar from './components/Calendar'
import SkillCertification from './components/SkillCertification'

function App() {
  const [currentView, setCurrentView] = useState('home')
  const [searchFilters, setSearchFilters] = useState({
    skills: '',
    experience: '',
    location: '',
    priceRange: ''
  })
  const [showAuthModal, setShowAuthModal] = useState(false)
  const [authMode, setAuthMode] = useState('login')

  const handleViewChange = (view) => {
    setCurrentView(view)
  }

  const handleFilterChange = (filters) => {
    setSearchFilters(filters)
  }

  const handleAuthAction = (mode) => {
    setAuthMode(mode)
    setShowAuthModal(true)
  }

  return (
    <div className="min-h-screen">
      <Header
        currentView={currentView}
        onViewChange={handleViewChange}
        onAuthAction={handleAuthAction}
      />

      <main className="pt-20">
        {currentView === 'home' && (
          <>
            <Hero onViewChange={handleViewChange} />
            <section className="py-20 relative bg-white/50">
              <div className="container">
                <div className="text-center mb-16">
                  <h2 className="text-4xl md:text-5xl font-bold text-primary-900 mb-6">
                    Why Choose <span className="text-gradient">PortfolioHub</span>?
                  </h2>
                  <p className="text-xl text-primary-700 max-w-3xl mx-auto">
                    Experience the next generation of freelancer-client connections with enhanced customization,
                    intelligent matching, and comprehensive analytics.
                  </p>
                </div>

                <div className="grid md:grid-cols-3 gap-8">
                  <div className="glass-card text-center group hover:scale-105 transition-all duration-500">
                    <div className="card-body">
                      <div className="w-16 h-16 bg-accent-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg group-hover:shadow-xl transition-shadow">
                        <User className="w-8 h-8 text-white" />
                      </div>
                      <h3 className="text-2xl font-bold mb-4 text-primary-900 group-hover:text-accent-600 transition-colors">Enhanced Customization</h3>
                      <p className="text-primary-600 leading-relaxed">
                        Create fully personalized profiles with advanced customization tools, unique branding options,
                        and interactive elements that make you stand out.
                      </p>
                    </div>
                  </div>

                  <div className="glass-card text-center group hover:scale-105 transition-all duration-500">
                    <div className="card-body">
                      <div className="w-16 h-16 bg-primary-700 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg group-hover:shadow-xl transition-shadow">
                        <Search className="w-8 h-8 text-white" />
                      </div>
                      <h3 className="text-2xl font-bold mb-4 text-primary-900 group-hover:text-primary-700 transition-colors">Smart Matching</h3>
                      <p className="text-primary-600 leading-relaxed">
                        Intelligent algorithms analyze skills, experience, and project requirements to create
                        perfect matches between freelancers and clients.
                      </p>
                    </div>
                  </div>

                  <div className="glass-card text-center group hover:scale-105 transition-all duration-500">
                    <div className="card-body">
                      <div className="w-16 h-16 bg-success-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg group-hover:shadow-xl transition-shadow">
                        <Star className="w-8 h-8 text-white" />
                      </div>
                      <h3 className="text-2xl font-bold mb-4 text-primary-900 group-hover:text-success-600 transition-colors">Professional Analytics</h3>
                      <p className="text-primary-600 leading-relaxed">
                        Comprehensive analytics dashboard with real-time insights, performance metrics,
                        and actionable recommendations to boost your success.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </section>
          </>
        )}

        {currentView === 'browse' && (
          <section className="py-8">
            <div className="container">
              <div className="flex flex-col lg:flex-row gap-8">
                <div className="lg:w-1/4">
                  <SearchFilters onFilterChange={handleFilterChange} />
                </div>
                <div className="lg:w-3/4">
                  <FreelancerProfiles filters={searchFilters} />
                </div>
              </div>
            </div>
          </section>
        )}

        {currentView === 'profile' && (
          <section className="py-8">
            <div className="container">
              <ProfileBuilder />
            </div>
          </section>
        )}

        {currentView === 'analytics' && (
          <section className="py-8">
            <div className="container">
              <AnalyticsDashboard />
            </div>
          </section>
        )}

        {currentView === 'calendar' && (
          <section className="py-8">
            <div className="container">
              <Calendar />
            </div>
          </section>
        )}

        {currentView === 'certifications' && (
          <section className="py-8">
            <div className="container">
              <SkillCertification />
            </div>
          </section>
        )}
      </main>

      <Footer />

      {/* Authentication Modal */}
      <AuthModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
        mode={authMode}
      />
    </div>
  )
}

export default App
