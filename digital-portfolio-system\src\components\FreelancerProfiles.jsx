import { useState, useEffect } from 'react'
import { Star, MapPin, Clock, DollarSign, Eye, MessageCircle, Heart } from 'lucide-react'

const FreelancerProfiles = ({ filters }) => {
  const [freelancers, setFreelancers] = useState([])
  const [loading, setLoading] = useState(true)

  // Mock data for demonstration
  const mockFreelancers = [
    {
      id: 1,
      name: '<PERSON>',
      title: 'Full-Stack Web Developer',
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
      location: 'San Francisco, CA',
      hourlyRate: 75,
      rating: 4.9,
      reviewCount: 127,
      skills: ['React', 'Node.js', 'TypeScript', 'MongoDB'],
      experience: 'Experienced (5-7 years)',
      availability: 'available',
      description: 'Passionate full-stack developer with expertise in modern web technologies. I help businesses build scalable web applications.',
      completedProjects: 89,
      responseTime: '2 hours',
      successRate: 98
    },
    {
      id: 2,
      name: '<PERSON>',
      title: 'UI/UX Designer',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
      location: 'New York, NY',
      hourlyRate: 60,
      rating: 4.8,
      reviewCount: 93,
      skills: ['Figma', 'Adobe XD', 'Prototyping', 'User Research'],
      experience: 'Intermediate (2-4 years)',
      availability: 'available',
      description: 'Creative UI/UX designer focused on creating intuitive and beautiful user experiences for web and mobile applications.',
      completedProjects: 67,
      responseTime: '1 hour',
      successRate: 96
    },
    {
      id: 3,
      name: 'Emily Rodriguez',
      title: 'Digital Marketing Specialist',
      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
      location: 'Austin, TX',
      hourlyRate: 45,
      rating: 4.7,
      reviewCount: 156,
      skills: ['SEO', 'Google Ads', 'Social Media', 'Content Strategy'],
      experience: 'Experienced (5-7 years)',
      availability: 'busy',
      description: 'Results-driven digital marketer helping businesses grow their online presence and increase conversions.',
      completedProjects: 134,
      responseTime: '4 hours',
      successRate: 94
    },
    {
      id: 4,
      name: 'David Kim',
      title: 'Mobile App Developer',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
      location: 'Seattle, WA',
      hourlyRate: 85,
      rating: 4.9,
      reviewCount: 78,
      skills: ['React Native', 'Flutter', 'iOS', 'Android'],
      experience: 'Expert (8+ years)',
      availability: 'available',
      description: 'Senior mobile developer specializing in cross-platform applications with native performance.',
      completedProjects: 52,
      responseTime: '3 hours',
      successRate: 99
    },
    {
      id: 5,
      name: 'Lisa Thompson',
      title: 'Content Writer & Copywriter',
      avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face',
      location: 'Chicago, IL',
      hourlyRate: 35,
      rating: 4.6,
      reviewCount: 201,
      skills: ['Content Writing', 'Copywriting', 'SEO Writing', 'Blog Writing'],
      experience: 'Intermediate (2-4 years)',
      availability: 'part-time',
      description: 'Professional writer creating engaging content that drives results for businesses across various industries.',
      completedProjects: 298,
      responseTime: '6 hours',
      successRate: 92
    },
    {
      id: 6,
      name: 'Alex Martinez',
      title: 'Data Analyst',
      avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face',
      location: 'Denver, CO',
      hourlyRate: 55,
      rating: 4.8,
      reviewCount: 89,
      skills: ['Python', 'SQL', 'Tableau', 'Machine Learning'],
      experience: 'Experienced (5-7 years)',
      availability: 'available',
      description: 'Data analyst with expertise in turning complex data into actionable insights for business growth.',
      completedProjects: 76,
      responseTime: '2 hours',
      successRate: 97
    }
  ]

  useEffect(() => {
    // Simulate API call
    setLoading(true)
    setTimeout(() => {
      setFreelancers(mockFreelancers)
      setLoading(false)
    }, 1000)
  }, [])

  const filteredFreelancers = freelancers.filter(freelancer => {
    // Apply filters
    if (filters.search && !freelancer.name.toLowerCase().includes(filters.search.toLowerCase()) &&
        !freelancer.title.toLowerCase().includes(filters.search.toLowerCase())) {
      return false
    }
    
    if (filters.skills.length > 0 && !filters.skills.some(skill => 
        freelancer.skills.some(fSkill => fSkill.toLowerCase().includes(skill.toLowerCase())))) {
      return false
    }
    
    if (filters.experience && freelancer.experience !== filters.experience) {
      return false
    }
    
    if (filters.location && !freelancer.location.toLowerCase().includes(filters.location.toLowerCase())) {
      return false
    }
    
    if (filters.availability && freelancer.availability !== filters.availability) {
      return false
    }
    
    return true
  })

  if (loading) {
    return (
      <div className="space-y-6">
        {[...Array(6)].map((_, i) => (
          <div key={i} className="card animate-pulse">
            <div className="card-body">
              <div className="flex items-start gap-4">
                <div className="w-16 h-16 bg-gray-200 rounded-full"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-1/3"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  <div className="h-3 bg-gray-200 rounded w-full"></div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900">
          {filteredFreelancers.length} Freelancers Found
        </h2>
        <select className="form-input w-auto">
          <option>Sort by Relevance</option>
          <option>Highest Rated</option>
          <option>Lowest Price</option>
          <option>Most Reviews</option>
        </select>
      </div>

      {filteredFreelancers.map((freelancer) => (
        <div key={freelancer.id} className="card hover:shadow-md transition-shadow">
          <div className="card-body">
            <div className="flex flex-col md:flex-row gap-6">
              {/* Avatar and Basic Info */}
              <div className="flex items-start gap-4">
                <img
                  src={freelancer.avatar}
                  alt={freelancer.name}
                  className="w-16 h-16 rounded-full object-cover"
                />
                <div className="flex-1">
                  <h3 className="text-xl font-semibold text-gray-900 mb-1">
                    {freelancer.name}
                  </h3>
                  <p className="text-primary-600 font-medium mb-2">
                    {freelancer.title}
                  </p>
                  <div className="flex items-center gap-4 text-sm text-gray-600 mb-3">
                    <div className="flex items-center gap-1">
                      <MapPin className="w-4 h-4" />
                      {freelancer.location}
                    </div>
                    <div className="flex items-center gap-1">
                      <DollarSign className="w-4 h-4" />
                      ${freelancer.hourlyRate}/hour
                    </div>
                  </div>
                  <div className="flex items-center gap-2 mb-3">
                    <div className="flex items-center gap-1">
                      <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                      <span className="font-medium">{freelancer.rating}</span>
                      <span className="text-gray-600">({freelancer.reviewCount} reviews)</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Description and Skills */}
              <div className="flex-1">
                <p className="text-gray-600 mb-4">
                  {freelancer.description}
                </p>
                <div className="flex flex-wrap gap-2 mb-4">
                  {freelancer.skills.map((skill) => (
                    <span
                      key={skill}
                      className="px-3 py-1 bg-primary-50 text-primary-700 rounded-full text-sm"
                    >
                      {skill}
                    </span>
                  ))}
                </div>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <span className="text-gray-500">Projects:</span>
                    <div className="font-medium">{freelancer.completedProjects}</div>
                  </div>
                  <div>
                    <span className="text-gray-500">Response:</span>
                    <div className="font-medium">{freelancer.responseTime}</div>
                  </div>
                  <div>
                    <span className="text-gray-500">Success Rate:</span>
                    <div className="font-medium">{freelancer.successRate}%</div>
                  </div>
                  <div>
                    <span className="text-gray-500">Status:</span>
                    <div className={`font-medium capitalize ${
                      freelancer.availability === 'available' ? 'text-green-600' :
                      freelancer.availability === 'busy' ? 'text-red-600' : 'text-yellow-600'
                    }`}>
                      {freelancer.availability}
                    </div>
                  </div>
                </div>
              </div>

              {/* Actions */}
              <div className="flex flex-col gap-2 min-w-[120px]">
                <button className="btn btn-primary">
                  <MessageCircle className="w-4 h-4" />
                  Contact
                </button>
                <button className="btn btn-outline">
                  <Eye className="w-4 h-4" />
                  View Profile
                </button>
                <button className="btn btn-secondary">
                  <Heart className="w-4 h-4" />
                  Save
                </button>
              </div>
            </div>
          </div>
        </div>
      ))}

      {filteredFreelancers.length === 0 && (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">
            <Search className="w-16 h-16 mx-auto" />
          </div>
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            No freelancers found
          </h3>
          <p className="text-gray-600">
            Try adjusting your filters to see more results.
          </p>
        </div>
      )}
    </div>
  )
}

export default FreelancerProfiles
