import { useState } from 'react'
import { User, Briefcase, GraduationCap, Award, Link, Save, Eye, Download, Share2 } from 'lucide-react'

const ProfileBuilder = () => {
  const [activeTab, setActiveTab] = useState('basic')
  const [profileData, setProfileData] = useState({
    basic: {
      firstName: '',
      lastName: '',
      title: '',
      bio: '',
      location: '',
      email: '',
      phone: '',
      website: ''
    },
    experience: [],
    education: [],
    skills: [],
    projects: [],
    certifications: [],
    socialLinks: {
      linkedin: '',
      twitter: '',
      github: '',
      instagram: '',
      behance: '',
      dribbble: ''
    }
  })

  const tabs = [
    { id: 'basic', label: 'Basic Info', icon: User },
    { id: 'experience', label: 'Experience', icon: Briefcase },
    { id: 'education', label: 'Education', icon: GraduationCap },
    { id: 'skills', label: 'Skills', icon: Award },
    { id: 'projects', label: 'Projects', icon: Briefcase },
    { id: 'social', label: 'Social Links', icon: Link }
  ]

  const handleBasicInfoChange = (field, value) => {
    setProfileData({
      ...profileData,
      basic: {
        ...profileData.basic,
        [field]: value
      }
    })
  }

  const addExperience = () => {
    setProfileData({
      ...profileData,
      experience: [
        ...profileData.experience,
        {
          id: Date.now(),
          title: '',
          company: '',
          location: '',
          startDate: '',
          endDate: '',
          current: false,
          description: ''
        }
      ]
    })
  }

  const addEducation = () => {
    setProfileData({
      ...profileData,
      education: [
        ...profileData.education,
        {
          id: Date.now(),
          degree: '',
          institution: '',
          location: '',
          startDate: '',
          endDate: '',
          gpa: '',
          description: ''
        }
      ]
    })
  }

  const addSkill = (skillName) => {
    if (skillName && !profileData.skills.find(s => s.name === skillName)) {
      setProfileData({
        ...profileData,
        skills: [
          ...profileData.skills,
          {
            id: Date.now(),
            name: skillName,
            level: 'Intermediate'
          }
        ]
      })
    }
  }

  const addProject = () => {
    setProfileData({
      ...profileData,
      projects: [
        ...profileData.projects,
        {
          id: Date.now(),
          title: '',
          description: '',
          technologies: [],
          liveUrl: '',
          githubUrl: '',
          imageUrl: '',
          startDate: '',
          endDate: ''
        }
      ]
    })
  }

  const handleSocialLinkChange = (platform, value) => {
    setProfileData({
      ...profileData,
      socialLinks: {
        ...profileData.socialLinks,
        [platform]: value
      }
    })
  }

  const renderBasicInfo = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            First Name *
          </label>
          <input
            type="text"
            value={profileData.basic.firstName}
            onChange={(e) => handleBasicInfoChange('firstName', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
            placeholder="John"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Last Name *
          </label>
          <input
            type="text"
            value={profileData.basic.lastName}
            onChange={(e) => handleBasicInfoChange('lastName', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
            placeholder="Doe"
          />
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Professional Title *
        </label>
        <input
          type="text"
          value={profileData.basic.title}
          onChange={(e) => handleBasicInfoChange('title', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
          placeholder="Full-Stack Developer"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Bio/Summary
        </label>
        <textarea
          value={profileData.basic.bio}
          onChange={(e) => handleBasicInfoChange('bio', e.target.value)}
          rows={4}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
          placeholder="Tell us about yourself, your expertise, and what makes you unique..."
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Location
          </label>
          <input
            type="text"
            value={profileData.basic.location}
            onChange={(e) => handleBasicInfoChange('location', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
            placeholder="San Francisco, CA"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Website
          </label>
          <input
            type="url"
            value={profileData.basic.website}
            onChange={(e) => handleBasicInfoChange('website', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
            placeholder="https://yourwebsite.com"
          />
        </div>
      </div>
    </div>
  )

  const renderSocialLinks = () => (
    <div className="space-y-4">
      <p className="text-gray-600">
        Connect your social media profiles to give employers a complete view of your professional presence.
      </p>
      
      {Object.entries(profileData.socialLinks).map(([platform, url]) => (
        <div key={platform}>
          <label className="block text-sm font-medium text-gray-700 mb-2 capitalize">
            {platform}
          </label>
          <input
            type="url"
            value={url}
            onChange={(e) => handleSocialLinkChange(platform, e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
            placeholder={`https://${platform}.com/yourprofile`}
          />
        </div>
      ))}
    </div>
  )

  return (
    <div className="max-w-6xl mx-auto">
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-bold text-gray-900">Profile Builder</h2>
            <div className="flex items-center space-x-3">
              <button className="btn btn-outline">
                <Eye className="w-4 h-4" />
                Preview
              </button>
              <button className="btn btn-outline">
                <Download className="w-4 h-4" />
                Export PDF
              </button>
              <button className="btn btn-outline">
                <Share2 className="w-4 h-4" />
                Share
              </button>
              <button className="btn btn-primary">
                <Save className="w-4 h-4" />
                Save Changes
              </button>
            </div>
          </div>
        </div>

        <div className="flex">
          {/* Sidebar Navigation */}
          <div className="w-64 border-r border-gray-200 p-4">
            <nav className="space-y-2">
              {tabs.map((tab) => {
                const Icon = tab.icon
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`w-full flex items-center space-x-3 px-3 py-2 rounded-md text-left transition-colors ${
                      activeTab === tab.id
                        ? 'bg-primary-50 text-primary-700 border-primary-200'
                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                    }`}
                  >
                    <Icon className="w-5 h-5" />
                    <span className="font-medium">{tab.label}</span>
                  </button>
                )
              })}
            </nav>
          </div>

          {/* Main Content */}
          <div className="flex-1 p-6">
            {activeTab === 'basic' && renderBasicInfo()}
            {activeTab === 'social' && renderSocialLinks()}
            
            {activeTab === 'experience' && (
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold">Work Experience</h3>
                  <button onClick={addExperience} className="btn btn-primary">
                    Add Experience
                  </button>
                </div>
                {profileData.experience.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    No experience added yet. Click "Add Experience" to get started.
                  </div>
                ) : (
                  <div className="space-y-4">
                    {/* Experience items would be rendered here */}
                    <p className="text-gray-600">Experience items will be displayed here...</p>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'skills' && (
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold">Skills & Expertise</h3>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Add Skill
                    </label>
                    <input
                      type="text"
                      placeholder="e.g., JavaScript, React, Node.js"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                      onKeyPress={(e) => {
                        if (e.key === 'Enter') {
                          addSkill(e.target.value)
                          e.target.value = ''
                        }
                      }}
                    />
                  </div>
                </div>
                <div className="flex flex-wrap gap-2">
                  {profileData.skills.map((skill) => (
                    <span
                      key={skill.id}
                      className="px-3 py-1 bg-primary-50 text-primary-700 rounded-full text-sm"
                    >
                      {skill.name} - {skill.level}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default ProfileBuilder
