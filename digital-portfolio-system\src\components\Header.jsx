import { useState } from 'react'
import { Menu, X, User, Search, Briefcase, BarChart3, Calendar, Award } from 'lucide-react'

const Header = ({ currentView, onViewChange, onAuthAction }) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  const navigation = [
    { name: 'Home', id: 'home', icon: Briefcase },
    { name: 'Browse Freelancers', id: 'browse', icon: Search },
    { name: 'Create Profile', id: 'profile', icon: User },
    { name: 'Analytics', id: 'analytics', icon: BarChart3 },
    { name: 'Calendar', id: 'calendar', icon: Calendar },
    { name: 'Certifications', id: 'certifications', icon: Award }
  ]

  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-gray-900/95 backdrop-blur-xl border-b border-gray-700 shadow-xl">
      <div className="container">
        <div className="flex items-center justify-between h-20">
          {/* Logo */}
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <h1 className="text-2xl font-bold text-white">
                PortfolioHub
              </h1>
            </div>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:block">
            <div className="flex items-center space-x-2">
              {navigation.map((item) => {
                const Icon = item.icon
                return (
                  <button
                    key={item.id}
                    onClick={() => onViewChange(item.id)}
                    className={`flex items-center gap-2 px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300 ${
                      currentView === item.id
                        ? 'text-white bg-accent-600 shadow-lg'
                        : 'text-gray-300 hover:text-white hover:bg-gray-800'
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    {item.name}
                  </button>
                )
              })}
            </div>
          </nav>

          {/* Desktop Auth Buttons */}
          <div className="hidden md:flex items-center space-x-4">
            <button
              onClick={() => onAuthAction && onAuthAction('login')}
              className="px-6 py-2 text-gray-300 hover:text-white font-medium rounded-xl hover:bg-gray-800 transition-all duration-300"
            >
              Sign In
            </button>
            <button
              onClick={() => onAuthAction && onAuthAction('register')}
              className="px-6 py-2 bg-accent-600 hover:bg-accent-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl hover:scale-105 transition-all duration-300"
            >
              Get Started
            </button>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="p-2 rounded-md text-gray-300 hover:text-white hover:bg-gray-800"
            >
              {isMenuOpen ? (
                <X className="w-6 h-6" />
              ) : (
                <Menu className="w-6 h-6" />
              )}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 border-t border-gray-700 bg-gray-900">
              {navigation.map((item) => {
                const Icon = item.icon
                return (
                  <button
                    key={item.id}
                    onClick={() => {
                      onViewChange(item.id)
                      setIsMenuOpen(false)
                    }}
                    className={`flex items-center gap-2 w-full px-3 py-2 rounded-md text-base font-medium transition-colors ${
                      currentView === item.id
                        ? 'text-white bg-accent-600'
                        : 'text-gray-300 hover:text-white hover:bg-gray-800'
                    }`}
                  >
                    <Icon className="w-5 h-5" />
                    {item.name}
                  </button>
                )
              })}
              <div className="pt-4 space-y-2">
                <button
                  onClick={() => {
                    onAuthAction && onAuthAction('login')
                    setIsMenuOpen(false)
                  }}
                  className="btn btn-outline w-full"
                >
                  Sign In
                </button>
                <button
                  onClick={() => {
                    onAuthAction && onAuthAction('register')
                    setIsMenuOpen(false)
                  }}
                  className="btn btn-primary w-full"
                >
                  Get Started
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </header>
  )
}

export default Header
