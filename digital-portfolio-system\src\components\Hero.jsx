import { ArrowRight, Users, Briefcase, TrendingUp, Sparkles, Star, Zap } from 'lucide-react'

const Hero = ({ onViewChange }) => {
  const stats = [
    { label: 'Active Freelancers', value: '10,000+', icon: Users, color: 'from-accent-500 to-accent-600' },
    { label: 'Projects Completed', value: '50,000+', icon: Briefcase, color: 'from-primary-600 to-primary-700' },
    { label: 'Success Rate', value: '98%', icon: TrendingUp, color: 'from-success-500 to-success-600' }
  ]

  const features = [
    { icon: Sparkles, text: 'AI-Powered Matching' },
    { icon: Star, text: 'Premium Quality' },
    { icon: Zap, text: 'Lightning Fast' }
  ]

  return (
    <section className="relative py-20 overflow-hidden bg-gradient-to-br from-primary-900 via-primary-800 to-primary-700">
      <div className="container relative z-10">
        <div className="text-center max-w-6xl mx-auto">
          {/* Badge */}
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-white/10 backdrop-blur-md rounded-full border border-white/20 mb-8">
            <Sparkles className="w-4 h-4 text-accent-400" />
            <span className="text-sm font-medium text-white">The Future of Professional Portfolios</span>
          </div>

          {/* Main Heading */}
          <h1 className="text-5xl md:text-7xl font-extrabold mb-8 leading-tight">
            <span className="text-white">Build Your</span>
            <br />
            <span className="text-gradient">
              Professional Portfolio
            </span>
            <br />
            <span className="text-white">Stand Out</span>
            <span className="text-accent-400"> Professionally</span>
          </h1>

          <p className="text-xl md:text-2xl text-white/90 mb-12 max-w-4xl mx-auto leading-relaxed">
            Create a <span className="font-semibold text-accent-300">professional, customizable portfolio</span> that showcases your skills,
            experience, and achievements. Connect with clients who value your expertise and
            <span className="font-semibold text-white"> advance your career</span>.
          </p>

          {/* Feature Pills */}
          <div className="flex flex-wrap justify-center gap-4 mb-12">
            {features.map((feature, index) => {
              const Icon = feature.icon
              return (
                <div key={index} className="flex items-center gap-2 px-4 py-2 bg-white/10 backdrop-blur-md rounded-full border border-white/20">
                  <Icon className="w-4 h-4 text-accent-300" />
                  <span className="text-sm font-medium text-white">{feature.text}</span>
                </div>
              )
            })}
          </div>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-6 justify-center mb-20">
            <button
              onClick={() => onViewChange('profile')}
              className="group px-8 py-4 bg-accent-600 hover:bg-accent-700 rounded-2xl font-semibold text-white text-lg shadow-xl transition-all duration-300 hover:scale-105"
            >
              <span className="flex items-center gap-2">
                Create Your Portfolio
                <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
              </span>
            </button>
            <button
              onClick={() => onViewChange('browse')}
              className="px-8 py-4 bg-white/10 backdrop-blur-md border-2 border-white/30 rounded-2xl font-semibold text-white text-lg hover:bg-white/20 transition-all duration-300 hover:scale-105"
            >
              Explore Talent
            </button>
          </div>

          {/* Enhanced Stats */}
          <div className="grid md:grid-cols-3 gap-8">
            {stats.map((stat, index) => {
              const Icon = stat.icon
              return (
                <div key={index} className="glass-card p-8 text-center group hover:scale-105 transition-all duration-300">
                  <div className={`w-16 h-16 bg-gradient-to-br ${stat.color} rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg group-hover:shadow-xl transition-shadow`}>
                    <Icon className="w-8 h-8 text-white" />
                  </div>
                  <div className="text-4xl font-bold text-primary-900 mb-2 group-hover:text-accent-600 transition-colors">
                    {stat.value}
                  </div>
                  <div className="text-primary-600 font-medium">
                    {stat.label}
                  </div>
                </div>
              )
            })}
          </div>
        </div>
      </div>


    </section>
  )
}

export default Hero
