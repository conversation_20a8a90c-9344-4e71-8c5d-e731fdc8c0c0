import { ArrowRight, Users, Briefcase, TrendingUp } from 'lucide-react'

const Hero = ({ onViewChange }) => {
  const stats = [
    { label: 'Active Freelancers', value: '10,000+', icon: Users },
    { label: 'Projects Completed', value: '50,000+', icon: Briefcase },
    { label: 'Success Rate', value: '98%', icon: TrendingUp }
  ]

  return (
    <section className="bg-gradient-to-br from-primary-50 to-white py-20">
      <div className="container">
        <div className="text-center max-w-4xl mx-auto">
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
            Build Your
            <span className="text-primary-600"> Digital Portfolio</span>
            <br />
            Stand Out from the Crowd
          </h1>
          
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            Create a personalized, professional portfolio that showcases your skills, 
            experience, and achievements. Connect with clients who value your expertise.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-16">
            <button 
              onClick={() => onViewChange('profile')}
              className="btn btn-primary text-lg px-8 py-4"
            >
              Create Your Profile
              <ArrowRight className="w-5 h-5" />
            </button>
            <button 
              onClick={() => onViewChange('browse')}
              className="btn btn-outline text-lg px-8 py-4"
            >
              Browse Freelancers
            </button>
          </div>
          
          {/* Stats */}
          <div className="grid md:grid-cols-3 gap-8">
            {stats.map((stat, index) => {
              const Icon = stat.icon
              return (
                <div key={index} className="text-center">
                  <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <Icon className="w-6 h-6 text-primary-600" />
                  </div>
                  <div className="text-3xl font-bold text-gray-900 mb-1">
                    {stat.value}
                  </div>
                  <div className="text-gray-600">
                    {stat.label}
                  </div>
                </div>
              )
            })}
          </div>
        </div>
      </div>
    </section>
  )
}

export default Hero
