import { useState } from 'react'
import { Award, Clock, Star, Play, CheckCircle, Trophy, Target } from 'lucide-react'

const SkillCertification = () => {
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [activeTest, setActiveTest] = useState(null)

  const categories = [
    { id: 'all', name: 'All Skills' },
    { id: 'development', name: 'Development' },
    { id: 'design', name: 'Design' },
    { id: 'marketing', name: 'Marketing' },
    { id: 'business', name: 'Business' }
  ]

  const skillTests = [
    {
      id: 1,
      title: 'JavaScript Fundamentals',
      category: 'development',
      difficulty: 'Beginner',
      duration: '45 minutes',
      questions: 30,
      passingScore: 70,
      description: 'Test your knowledge of JavaScript basics including variables, functions, and DOM manipulation.',
      skills: ['Variables', 'Functions', 'DOM', 'Events'],
      completed: true,
      score: 85,
      badge: 'JavaScript Certified'
    },
    {
      id: 2,
      title: 'React Development',
      category: 'development',
      difficulty: 'Intermediate',
      duration: '60 minutes',
      questions: 40,
      passingScore: 75,
      description: 'Comprehensive test covering React components, hooks, state management, and best practices.',
      skills: ['Components', 'Hooks', 'State', 'Props'],
      completed: false,
      score: null,
      badge: 'React Expert'
    },
    {
      id: 3,
      title: 'UI/UX Design Principles',
      category: 'design',
      difficulty: 'Intermediate',
      duration: '50 minutes',
      questions: 35,
      passingScore: 70,
      description: 'Evaluate your understanding of design principles, user experience, and interface design.',
      skills: ['Design Theory', 'User Research', 'Prototyping', 'Accessibility'],
      completed: true,
      score: 92,
      badge: 'UX Designer'
    },
    {
      id: 4,
      title: 'Digital Marketing Strategy',
      category: 'marketing',
      difficulty: 'Advanced',
      duration: '75 minutes',
      questions: 50,
      passingScore: 80,
      description: 'Advanced test on digital marketing strategies, analytics, and campaign optimization.',
      skills: ['SEO', 'Social Media', 'Analytics', 'Campaign Management'],
      completed: false,
      score: null,
      badge: 'Marketing Strategist'
    },
    {
      id: 5,
      title: 'Project Management',
      category: 'business',
      difficulty: 'Intermediate',
      duration: '55 minutes',
      questions: 45,
      passingScore: 75,
      description: 'Test your project management skills including planning, execution, and team leadership.',
      skills: ['Planning', 'Agile', 'Leadership', 'Risk Management'],
      completed: true,
      score: 78,
      badge: 'Project Manager'
    },
    {
      id: 6,
      title: 'Node.js Backend Development',
      category: 'development',
      difficulty: 'Advanced',
      duration: '90 minutes',
      questions: 55,
      passingScore: 80,
      description: 'Advanced Node.js test covering APIs, databases, authentication, and server architecture.',
      skills: ['Express.js', 'MongoDB', 'Authentication', 'APIs'],
      completed: false,
      score: null,
      badge: 'Backend Expert'
    }
  ]

  const getDifficultyColor = (difficulty) => {
    switch (difficulty) {
      case 'Beginner': return 'text-green-600 bg-green-100'
      case 'Intermediate': return 'text-yellow-600 bg-yellow-100'
      case 'Advanced': return 'text-red-600 bg-red-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const filteredTests = selectedCategory === 'all' 
    ? skillTests 
    : skillTests.filter(test => test.category === selectedCategory)

  const completedTests = skillTests.filter(test => test.completed)
  const averageScore = completedTests.length > 0 
    ? Math.round(completedTests.reduce((sum, test) => sum + test.score, 0) / completedTests.length)
    : 0

  return (
    <div className="max-w-7xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Skill Certification</h1>
          <p className="text-gray-600 mt-2">
            Validate your expertise with professional skill assessments
          </p>
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Completed Tests</p>
              <p className="text-3xl font-bold text-gray-900 mt-2">{completedTests.length}</p>
            </div>
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <CheckCircle className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Average Score</p>
              <p className="text-3xl font-bold text-gray-900 mt-2">{averageScore}%</p>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <Target className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Badges Earned</p>
              <p className="text-3xl font-bold text-gray-900 mt-2">{completedTests.length}</p>
            </div>
            <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
              <Award className="w-6 h-6 text-yellow-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Skill Rank</p>
              <p className="text-3xl font-bold text-gray-900 mt-2">Top 15%</p>
            </div>
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <Trophy className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Category Filter */}
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <div className="flex flex-wrap gap-2">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => setSelectedCategory(category.id)}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                selectedCategory === category.id
                  ? 'bg-primary-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {category.name}
            </button>
          ))}
        </div>
      </div>

      {/* Skill Tests Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredTests.map((test) => (
          <div key={test.id} className="bg-white rounded-lg border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
            <div className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">{test.title}</h3>
                  <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(test.difficulty)}`}>
                    {test.difficulty}
                  </span>
                </div>
                {test.completed && (
                  <div className="flex items-center space-x-1">
                    <CheckCircle className="w-5 h-5 text-green-500" />
                    <span className="text-sm font-medium text-green-600">{test.score}%</span>
                  </div>
                )}
              </div>

              <p className="text-gray-600 text-sm mb-4">{test.description}</p>

              <div className="space-y-2 mb-4">
                <div className="flex items-center text-sm text-gray-500">
                  <Clock className="w-4 h-4 mr-2" />
                  {test.duration} • {test.questions} questions
                </div>
                <div className="flex items-center text-sm text-gray-500">
                  <Star className="w-4 h-4 mr-2" />
                  Passing score: {test.passingScore}%
                </div>
              </div>

              <div className="mb-4">
                <p className="text-sm font-medium text-gray-700 mb-2">Skills covered:</p>
                <div className="flex flex-wrap gap-1">
                  {test.skills.map((skill, index) => (
                    <span
                      key={index}
                      className="px-2 py-1 bg-gray-100 text-gray-600 rounded text-xs"
                    >
                      {skill}
                    </span>
                  ))}
                </div>
              </div>

              {test.completed ? (
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">Your Score:</span>
                    <span className="font-medium text-gray-900">{test.score}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-green-500 h-2 rounded-full"
                      style={{ width: `${test.score}%` }}
                    ></div>
                  </div>
                  <div className="flex items-center text-sm text-green-600">
                    <Award className="w-4 h-4 mr-1" />
                    Badge earned: {test.badge}
                  </div>
                  <button className="w-full btn btn-outline mt-3">
                    View Certificate
                  </button>
                </div>
              ) : (
                <button 
                  onClick={() => setActiveTest(test.id)}
                  className="w-full btn btn-primary"
                >
                  <Play className="w-4 h-4" />
                  Start Test
                </button>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Earned Badges Section */}
      {completedTests.length > 0 && (
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Your Badges</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {completedTests.map((test) => (
              <div key={test.id} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                <div className="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center">
                  <Award className="w-5 h-5 text-yellow-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900">{test.badge}</p>
                  <p className="text-xs text-gray-500">Score: {test.score}%</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

export default SkillCertification
