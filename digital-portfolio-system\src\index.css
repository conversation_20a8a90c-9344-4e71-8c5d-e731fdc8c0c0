/* Modern CSS Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Test CSS to verify loading */
body {
  background-color: #f9fafb !important;
  font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  color: #111827;
}

/* Basic utility classes with direct values */
.min-h-screen {
  min-height: 100vh;
}

.bg-gray-50 {
  background-color: #f9fafb;
}

.bg-white {
  background-color: white;
}

.bg-primary-600 {
  background-color: #2563eb;
}

.bg-primary-50 {
  background-color: #eff6ff;
}

.text-primary-600 {
  color: #2563eb;
}

.text-gray-900 {
  color: #111827;
}

.text-gray-600 {
  color: #4b5563;
}

.text-white {
  color: white;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.h-16 {
  height: 4rem;
}

.shadow-sm {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.border-b {
  border-bottom-width: 1px;
}

.border-gray-200 {
  border-color: #e5e7eb;
}

.text-2xl {
  font-size: 1.5rem;
}

.font-bold {
  font-weight: 700;
}

.py-20 {
  padding-top: 5rem;
  padding-bottom: 5rem;
}

.text-center {
  text-align: center;
}

.text-4xl {
  font-size: 2.25rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.mb-8 {
  margin-bottom: 2rem;
}

.text-xl {
  font-size: 1.25rem;
}

.max-w-2xl {
  max-width: 42rem;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: 0.5rem;
  border: 1px solid transparent;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.btn-primary {
  background-color: #2563eb;
  color: white;
}

.btn-primary:hover {
  background-color: #1d4ed8;
  color: white;
  text-decoration: none;
}

.btn-outline {
  background-color: transparent;
  color: #2563eb;
  border-color: #2563eb;
}

.btn-outline:hover {
  background-color: #2563eb;
  color: white;
  text-decoration: none;
}

/* Additional utility classes */
.hidden {
  display: none;
}

.md\:block {
  display: block;
}

.md\:hidden {
  display: none;
}

.space-x-8 > * + * {
  margin-left: 2rem;
}

.space-x-4 > * + * {
  margin-left: 1rem;
}

.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}

.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.rounded-md {
  border-radius: 0.375rem;
}

.text-sm {
  font-size: 0.875rem;
}

.font-medium {
  font-weight: 500;
}

.transition-colors {
  transition-property: color, background-color, border-color;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.hover\:text-gray-900:hover {
  color: #111827;
}

.hover\:bg-gray-50:hover {
  background-color: #f9fafb;
}

.w-4 {
  width: 1rem;
}

.h-4 {
  height: 1rem;
}

.w-5 {
  width: 1.25rem;
}

.h-5 {
  height: 1.25rem;
}

.w-6 {
  width: 1.5rem;
}

.h-6 {
  height: 1.5rem;
}

.gap-2 {
  gap: 0.5rem;
}

.p-2 {
  padding: 0.5rem;
}

.bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, #eff6ff, white);
}

.grid {
  display: grid;
}

.md\:grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

.gap-8 {
  gap: 2rem;
}

.card {
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

.card-body {
  padding: 1.5rem;
}

.w-12 {
  width: 3rem;
}

.h-12 {
  height: 3rem;
}

.bg-primary-100 {
  background-color: #dbeafe;
}

.rounded-lg {
  border-radius: 0.5rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-3 {
  margin-bottom: 0.75rem;
}

.text-3xl {
  font-size: 1.875rem;
}

.mb-1 {
  margin-bottom: 0.25rem;
}

/* Responsive utilities */
@media (min-width: 768px) {
  .md\:block {
    display: block;
  }

  .md\:hidden {
    display: none;
  }

  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .md\:text-6xl {
    font-size: 3.75rem;
  }
}

html {
  font-family: var(--font-sans);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  min-height: 100vh;
  background-color: var(--gray-50);
  color: var(--gray-900);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.25;
  color: var(--gray-900);
}

h1 { font-size: 2.25rem; }
h2 { font-size: 1.875rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }
h5 { font-size: 1.125rem; }
h6 { font-size: 1rem; }

p {
  margin-bottom: var(--space-4);
  color: var(--gray-600);
}

/* Links */
a {
  color: var(--primary-600);
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover {
  color: var(--primary-700);
  text-decoration: underline;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-6);
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: var(--radius-md);
  border: 1px solid transparent;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.btn-primary {
  background-color: var(--primary-600);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-700);
  color: white;
  text-decoration: none;
}

.btn-secondary {
  background-color: white;
  color: var(--gray-700);
  border-color: var(--gray-300);
}

.btn-secondary:hover {
  background-color: var(--gray-50);
  color: var(--gray-900);
  text-decoration: none;
}

.btn-outline {
  background-color: transparent;
  color: var(--primary-600);
  border-color: var(--primary-600);
}

.btn-outline:hover {
  background-color: var(--primary-600);
  color: white;
  text-decoration: none;
}

/* Form Elements */
.form-input {
  width: 100%;
  padding: var(--space-3);
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
}

.form-label {
  display: block;
  margin-bottom: var(--space-2);
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--gray-700);
}

/* Cards */
.card {
  background-color: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
  overflow: hidden;
}

.card-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--gray-200);
}

.card-body {
  padding: var(--space-6);
}

.card-footer {
  padding: var(--space-6);
  border-top: 1px solid var(--gray-200);
  background-color: var(--gray-50);
}

/* Utility Classes */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-4 {
  gap: var(--space-4);
}

.gap-6 {
  gap: var(--space-6);
}

.text-center {
  text-align: center;
}

.text-sm {
  font-size: 0.875rem;
}

.text-lg {
  font-size: 1.125rem;
}

.text-xl {
  font-size: 1.25rem;
}

.font-medium {
  font-weight: 500;
}

.font-semibold {
  font-weight: 600;
}

.text-gray-500 {
  color: var(--gray-500);
}

.text-gray-600 {
  color: var(--gray-600);
}

.text-gray-900 {
  color: var(--gray-900);
}

.mb-4 {
  margin-bottom: var(--space-4);
}

.mb-6 {
  margin-bottom: var(--space-6);
}

.mb-8 {
  margin-bottom: var(--space-8);
}

.mt-4 {
  margin-top: var(--space-4);
}

.mt-8 {
  margin-top: var(--space-8);
}

.py-4 {
  padding-top: var(--space-4);
  padding-bottom: var(--space-4);
}

.py-8 {
  padding-top: var(--space-8);
  padding-bottom: var(--space-8);
}

.px-4 {
  padding-left: var(--space-4);
  padding-right: var(--space-4);
}

.px-6 {
  padding-left: var(--space-6);
  padding-right: var(--space-6);
}

/* Responsive */
@media (max-width: 768px) {
  .container {
    padding: 0 var(--space-4);
  }

  h1 { font-size: 1.875rem; }
  h2 { font-size: 1.5rem; }
  h3 { font-size: 1.25rem; }
}
